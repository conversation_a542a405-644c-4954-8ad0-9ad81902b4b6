import {
  doc,
  setDoc,
  getDoc,
  addDoc,
  collection,
  serverTimestamp,
  Timestamp,
  query,
  where,
  orderBy,
  getDocs,
  updateDoc,
  deleteDoc
} from 'firebase/firestore'
import { db } from './firebase'
import { User } from 'firebase/auth'
import { ChatSession, Message, FirestoreChatSession, FirestoreMessage } from '@/types/chat'

// Available user tiers
export const USER_TIERS = {
  STARTER: 'Starter',
  PRO: 'Pro',
  BUSINESS: 'Business',
  PREMIUM: 'Premium'
} as const

export type UserTier = typeof USER_TIERS[keyof typeof USER_TIERS]

// User document interface
export interface UserDocument {
  uid: string
  fullName: string
  email: string
  tier: UserTier
  createdAt: Timestamp
  updatedAt: Timestamp
}

// Create user document in Firestore
export const createUserDocument = async (
  user: User,
  fullName: string
): Promise<void> => {
  try {
    const userDocRef = doc(db, 'users', user.uid)

    // Check if user document already exists
    const userDocSnap = await getDoc(userDocRef)

    if (!userDocSnap.exists()) {
      const userData: Omit<UserDocument, 'createdAt' | 'updatedAt'> & {
        createdAt: any
        updatedAt: any
      } = {
        uid: user.uid,
        fullName: fullName,
        email: user.email || '',
        tier: USER_TIERS.STARTER, // Default tier for new users
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      }

      await setDoc(userDocRef, userData)
      console.log('User document created successfully')
    } else {
      console.log('User document already exists')
    }
  } catch (error) {
    console.error('Error creating user document:', error)
    throw new Error('Failed to create user profile')
  }
}

// Get user document from Firestore
export const getUserDocument = async (uid: string): Promise<UserDocument | null> => {
  try {
    const userDocRef = doc(db, 'users', uid)
    const userDocSnap = await getDoc(userDocRef)

    if (userDocSnap.exists()) {
      return userDocSnap.data() as UserDocument
    } else {
      return null
    }
  } catch (error) {
    console.error('Error getting user document:', error)
    throw new Error('Failed to get user profile')
  }
}

// Update user document in Firestore
export const updateUserDocument = async (
  uid: string,
  updates: Partial<Omit<UserDocument, 'uid' | 'createdAt'>>
): Promise<void> => {
  try {
    const userDocRef = doc(db, 'users', uid)

    const updateData = {
      ...updates,
      updatedAt: serverTimestamp(),
    }

    await setDoc(userDocRef, updateData, { merge: true })
    console.log('User document updated successfully')
  } catch (error) {
    console.error('Error updating user document:', error)
    throw new Error('Failed to update user profile')
  }
}

// Update user tier specifically
export const updateUserTier = async (uid: string, newTier: UserTier): Promise<void> => {
  try {
    await updateUserDocument(uid, { tier: newTier })
    console.log(`User tier updated to: ${newTier}`)
  } catch (error) {
    console.error('Error updating user tier:', error)
    throw new Error('Failed to update user tier')
  }
}

// Check if user has access to a specific tier or higher
export const hasAccessToTier = (userTier: UserTier, requiredTier: UserTier): boolean => {
  const tierHierarchy = [
    USER_TIERS.STARTER,
    USER_TIERS.PRO,
    USER_TIERS.BUSINESS,
    USER_TIERS.PREMIUM
  ]

  const userTierIndex = tierHierarchy.indexOf(userTier)
  const requiredTierIndex = tierHierarchy.indexOf(requiredTier)

  return userTierIndex >= requiredTierIndex
}

// Contact message interface
export interface ContactMessage {
  id?: string
  name: string
  email: string
  subject: string
  message: string
  phone?: string
  createdAt: Timestamp
  status: 'new' | 'read' | 'responded'
  userAgent?: string
  ipAddress?: string
}

// Create contact message in Firestore
export const createContactMessage = async (
  messageData: Omit<ContactMessage, 'id' | 'createdAt' | 'status'>
): Promise<string> => {
  try {
    const contactData: Omit<ContactMessage, 'id'> = {
      ...messageData,
      createdAt: serverTimestamp() as Timestamp,
      status: 'new'
    }

    const docRef = await addDoc(collection(db, 'contact_messages'), contactData)
    console.log('Contact message created successfully with ID:', docRef.id)
    return docRef.id
  } catch (error) {
    console.error('Error creating contact message:', error)
    throw new Error('Failed to send contact message')
  }
}

// ===== CHAT CONVERSATION FUNCTIONS =====

// Helper function to convert Firestore data to app data
const convertFirestoreToAppData = (firestoreSession: FirestoreChatSession & { id: string }): ChatSession => {
  return {
    id: firestoreSession.id,
    title: firestoreSession.title,
    userId: firestoreSession.userId,
    model: firestoreSession.model,
    createdAt: firestoreSession.createdAt.toDate(),
    updatedAt: firestoreSession.updatedAt.toDate(),
    messages: firestoreSession.messages.map((msg: FirestoreMessage): Message => ({
      id: msg.id,
      content: msg.content,
      role: msg.role,
      timestamp: msg.timestamp.toDate(),
      status: msg.status
    }))
  }
}

// Helper function to convert app data to Firestore data
const convertAppToFirestoreData = (session: Omit<ChatSession, 'id'>): Omit<FirestoreChatSession, 'id'> => {
  return {
    title: session.title,
    userId: session.userId,
    model: session.model,
    createdAt: serverTimestamp(),
    updatedAt: serverTimestamp(),
    messages: session.messages.map((msg: Message): FirestoreMessage => ({
      id: msg.id,
      content: msg.content,
      role: msg.role,
      timestamp: Timestamp.fromDate(msg.timestamp), // Use Timestamp.fromDate instead of serverTimestamp
      status: msg.status
    }))
  }
}

// Create a new chat session
export const createChatSession = async (
  sessionData: Omit<ChatSession, 'id' | 'createdAt' | 'updatedAt'>
): Promise<string> => {
  try {
    const firestoreData = convertAppToFirestoreData({
      ...sessionData,
      createdAt: new Date(),
      updatedAt: new Date()
    })

    const docRef = await addDoc(collection(db, 'chat_sessions'), firestoreData)
    console.log('Chat session created successfully with ID:', docRef.id)
    return docRef.id
  } catch (error) {
    console.error('Error creating chat session:', error)
    throw new Error('Failed to create chat session')
  }
}

// Get all chat sessions for a user
export const getUserChatSessions = async (userId: string): Promise<ChatSession[]> => {
  try {
    const q = query(
      collection(db, 'chat_sessions'),
      where('userId', '==', userId),
      orderBy('updatedAt', 'desc')
    )

    const querySnapshot = await getDocs(q)
    const sessions: ChatSession[] = []

    querySnapshot.forEach((doc) => {
      const data = doc.data() as FirestoreChatSession
      sessions.push(convertFirestoreToAppData({ ...data, id: doc.id }))
    })

    return sessions
  } catch (error) {
    console.error('Error getting user chat sessions:', error)
    throw new Error('Failed to load chat sessions')
  }
}

// Get a specific chat session
export const getChatSession = async (sessionId: string): Promise<ChatSession | null> => {
  try {
    const docRef = doc(db, 'chat_sessions', sessionId)
    const docSnap = await getDoc(docRef)

    if (docSnap.exists()) {
      const data = docSnap.data() as FirestoreChatSession
      return convertFirestoreToAppData({ ...data, id: docSnap.id })
    } else {
      return null
    }
  } catch (error) {
    console.error('Error getting chat session:', error)
    throw new Error('Failed to load chat session')
  }
}

// Update a chat session (typically to add new messages)
export const updateChatSession = async (
  sessionId: string,
  updates: Partial<Omit<ChatSession, 'id' | 'createdAt'>>
): Promise<void> => {
  try {
    const docRef = doc(db, 'chat_sessions', sessionId)

    const updateData: any = {
      updatedAt: serverTimestamp()
    }

    // Handle messages update
    if (updates.messages) {
      updateData.messages = updates.messages.map((msg: Message): FirestoreMessage => ({
        id: msg.id,
        content: msg.content,
        role: msg.role,
        timestamp: Timestamp.fromDate(msg.timestamp), // Use Timestamp.fromDate instead of serverTimestamp
        status: msg.status
      }))
    }

    // Handle other updates
    if (updates.title) updateData.title = updates.title
    if (updates.model) updateData.model = updates.model

    await updateDoc(docRef, updateData)
    console.log('Chat session updated successfully')
  } catch (error) {
    console.error('Error updating chat session:', error)
    throw new Error('Failed to update chat session')
  }
}

// Delete a chat session
export const deleteChatSession = async (sessionId: string): Promise<void> => {
  try {
    const docRef = doc(db, 'chat_sessions', sessionId)
    await deleteDoc(docRef)
    console.log('Chat session deleted successfully')
  } catch (error) {
    console.error('Error deleting chat session:', error)
    throw new Error('Failed to delete chat session')
  }
}

// Generate a title for a chat session based on the first user message
export const generateChatTitle = (firstMessage: string): string => {
  // Take first 50 characters and add ellipsis if longer
  const maxLength = 50
  if (firstMessage.length <= maxLength) {
    return firstMessage
  }
  return firstMessage.substring(0, maxLength).trim() + '...'
}
